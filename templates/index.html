<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能对话图片生成工具</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
</head>
<body>
    <div class="container">
        <header>
            <h1>🎨 智能对话图片生成工具</h1>
            <p>基于DeepSeek + fla.ai的智能图片生成</p>
        </header>

        <div class="chat-container">
            <div id="chat-messages" class="chat-messages">
                <div class="message system-message">
                    <div class="message-content">
                        <p>👋 欢迎使用智能图片生成工具！</p>
                        <p>您可以：</p>
                        <ul>
                            <li>📝 输入文字描述生成图片</li>
                            <li>🖼️ 上传图片+文字描述进行图片转换</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="input-container">
                <form id="generate-form" enctype="multipart/form-data">
                    <div class="input-group">
                        <div class="text-input-container">
                            <textarea 
                                id="text-input" 
                                name="text" 
                                placeholder="请描述您想要生成的图片..."
                                rows="3"
                            ></textarea>
                        </div>
                        
                        <div class="file-input-container">
                            <input type="file" id="image-input" name="image" accept="image/*" style="display: none;">
                            <button type="button" id="upload-btn" class="upload-btn">
                                📎 上传图片
                            </button>
                            <div id="image-preview" class="image-preview" style="display: none;">
                                <img id="preview-img" src="" alt="预览图片">
                                <button type="button" id="remove-image" class="remove-btn">✕</button>
                            </div>
                        </div>
                    </div>
                    
                    <button type="submit" id="generate-btn" class="generate-btn">
                        ✨ 生成图片
                    </button>
                </form>
            </div>
        </div>

        <div id="loading" class="loading" style="display: none;">
            <div class="loading-spinner"></div>
            <p>正在生成图片，请稍候...</p>
        </div>
    </div>

    <script src="{{ url_for('static', filename='script.js') }}"></script>
</body>
</html>
