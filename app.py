import os
import requests
import base64
from flask import Flask, render_template, request, jsonify
from werkzeug.utils import secure_filename
from PIL import Image
import io
import json
import fal_client

app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
app.config['UPLOAD_FOLDER'] = 'uploads'

# API配置
DEEPSEEK_API_KEY = "***********************************"
FAL_API_KEY = "ee565634-3a0c-4cce-a994-1935010cf7c6:f8ec78ccab17e2e0f0f95d67ea2372c3"

DEEPSEEK_BASE_URL = "https://api.deepseek.com/v1/chat/completions"

# 设置fal.ai API密钥
os.environ['FAL_KEY'] = FAL_API_KEY

# 确保上传目录存在
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

def call_deepseek_api(user_input, has_image=False):
    """调用DeepSeek API生成优化的提示词"""
    
    if has_image:
        system_prompt = """你是一个专业的AI图像生成提示词专家。用户会提供一张图片和相关描述，请基于用户的需求生成一个优化的英文提示词，用于图生图任务。

要求：
1. 提示词要详细、具体、富有表现力
2. 包含风格、光线、构图等关键元素
3. 使用英文输出
4. 长度控制在100-200个单词
5. 重点关注用户想要的变化和改进"""
    else:
        system_prompt = """你是一个专业的AI图像生成提示词专家。用户会描述他们想要生成的图像，请将用户的描述转换为一个优化的英文提示词，用于文生图任务。

要求：
1. 提示词要详细、具体、富有表现力
2. 包含风格、光线、构图、色彩等关键元素
3. 使用英文输出
4. 长度控制在100-200个单词
5. 确保提示词能生成高质量的图像"""

    headers = {
        "Authorization": f"Bearer {DEEPSEEK_API_KEY}",
        "Content-Type": "application/json"
    }
    
    data = {
        "model": "deepseek-chat",
        "messages": [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_input}
        ],
        "temperature": 0.7,
        "max_tokens": 300
    }
    
    try:
        response = requests.post(DEEPSEEK_BASE_URL, headers=headers, json=data)
        response.raise_for_status()
        result = response.json()
        return result['choices'][0]['message']['content'].strip()
    except Exception as e:
        print(f"DeepSeek API调用失败: {e}")
        return None

def call_fal_text_to_image(prompt):
    """调用fal.ai文生图API"""
    try:
        handler = fal_client.submit(
            "fal-ai/flux/dev",
            arguments={
                "prompt": prompt,
                "image_size": "landscape_4_3",
                "num_inference_steps": 28,
                "guidance_scale": 3.5,
                "num_images": 1,
                "enable_safety_checker": True
            }
        )
        result = handler.get()

        if result and 'images' in result and len(result['images']) > 0:
            return result['images'][0]['url']
        return None
    except Exception as e:
        print(f"fal.ai文生图API调用失败: {e}")
        return None

def call_fal_image_to_image(prompt, image_path):
    """调用fal.ai图生图API"""
    try:
        # 上传图片到fal.ai存储
        with open(image_path, 'rb') as f:
            image_url = fal_client.upload_file(f)

        handler = fal_client.submit(
            "fal-ai/flux/dev",
            arguments={
                "prompt": prompt,
                "image_url": image_url,
                "image_size": "landscape_4_3",
                "num_inference_steps": 28,
                "guidance_scale": 3.5,
                "num_images": 1,
                "enable_safety_checker": True,
                "strength": 0.7
            }
        )
        result = handler.get()

        if result and 'images' in result and len(result['images']) > 0:
            return result['images'][0]['url']
        return None
    except Exception as e:
        print(f"fal.ai图生图API调用失败: {e}")
        return None

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/generate', methods=['POST'])
def generate_image():
    try:
        user_text = request.form.get('text', '').strip()
        uploaded_file = request.files.get('image')
        
        # 检查输入类型
        has_image = uploaded_file and uploaded_file.filename != ''
        has_text = bool(user_text)
        
        if not has_text and not has_image:
            return jsonify({'error': '请提供文字描述或上传图片'}), 400
        
        if has_image and not has_text:
            return jsonify({'error': '请为图片添加文字描述'}), 400
        
        # 处理图片上传
        image_path = None
        if has_image:
            filename = secure_filename(uploaded_file.filename)
            image_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            uploaded_file.save(image_path)
        
        # 调用DeepSeek生成优化提示词
        optimized_prompt = call_deepseek_api(user_text, has_image)
        if not optimized_prompt:
            return jsonify({'error': 'DeepSeek API调用失败'}), 500
        
        # 调用fal.ai生成图片
        if has_image:
            # 图生图
            image_url = call_fal_image_to_image(optimized_prompt, image_path)
            # 清理临时文件
            if os.path.exists(image_path):
                os.remove(image_path)
        else:
            # 文生图
            image_url = call_fal_text_to_image(optimized_prompt)
        
        if not image_url:
            return jsonify({'error': 'Fla.ai API调用失败'}), 500
        
        return jsonify({
            'success': True,
            'original_text': user_text,
            'optimized_prompt': optimized_prompt,
            'image_url': image_url,
            'generation_type': '图生图' if has_image else '文生图'
        })
        
    except Exception as e:
        print(f"生成图片时发生错误: {e}")
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
