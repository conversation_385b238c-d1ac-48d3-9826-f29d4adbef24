# 智能对话图片生成工具

基于DeepSeek和fla.ai的智能图片生成应用，支持文生图和图生图功能。

## 功能特性

- 🎨 **文生图**：输入文字描述，生成对应图片
- 🖼️ **图生图**：上传图片+文字描述，进行图片转换
- 🤖 **智能提示词优化**：使用DeepSeek作为提示词专家，自动优化生成效果
- 💬 **对话式界面**：类似ChatGPT的聊天体验
- 📱 **响应式设计**：支持桌面和移动设备

## 技术架构

- **后端**：Python + Flask
- **前端**：原生HTML/CSS/JavaScript
- **AI服务**：
  - DeepSeek API：提示词优化
  - fla.ai API：图片生成

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 启动应用

```bash
python3 app.py
```

### 3. 访问应用

打开浏览器访问：http://127.0.0.1:5000

## 使用方法

### 文生图
1. 在文本框中输入图片描述
2. 点击"生成图片"按钮
3. 等待DeepSeek优化提示词并生成图片

### 图生图
1. 点击"上传图片"按钮选择图片
2. 在文本框中输入想要的变化描述
3. 点击"生成图片"按钮
4. 等待处理完成

## 项目结构

```
├── app.py              # 主应用文件
├── requirements.txt    # Python依赖
├── templates/          # HTML模板
│   └── index.html     # 主页面
├── static/            # 静态文件
│   ├── style.css      # 样式文件
│   └── script.js      # 前端交互
├── uploads/           # 临时图片存储
└── README.md          # 项目说明
```

## API配置

应用中已配置了API密钥，如需修改请编辑`app.py`文件中的以下变量：

```python
DEEPSEEK_API_KEY = "your-deepseek-api-key"
FLA_API_KEY = "your-fla-api-key"
```

## 部署到GitHub

1. 初始化Git仓库：
```bash
git init
git add .
git commit -m "Initial commit"
```

2. 创建GitHub仓库并推送：
```bash
git remote add origin https://github.com/your-username/your-repo.git
git push -u origin main
```

## 注意事项

- 确保网络连接正常，应用需要调用外部API
- 上传的图片大小限制为16MB
- 生成图片可能需要几秒到几十秒的时间
- 建议使用现代浏览器以获得最佳体验

## 故障排除

### 常见问题

1. **API调用失败**
   - 检查网络连接
   - 确认API密钥是否正确
   - 查看控制台错误信息

2. **图片上传失败**
   - 确认图片格式支持（jpg, png, gif等）
   - 检查图片大小是否超过限制

3. **页面无法访问**
   - 确认Flask应用正在运行
   - 检查端口5000是否被占用

## 开发计划

- [ ] 添加更多图片生成参数配置
- [ ] 支持批量图片生成
- [ ] 添加图片历史记录
- [ ] 优化移动端体验
- [ ] 添加用户认证功能
