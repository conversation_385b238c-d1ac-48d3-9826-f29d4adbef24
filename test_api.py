#!/usr/bin/env python3
"""
API测试脚本
用于测试DeepSeek和fla.ai API的连接性
"""

import requests
import json
import fal_client
import os

# API配置
DEEPSEEK_API_KEY = "***********************************"
FAL_API_KEY = "ee565634-3a0c-4cce-a994-1935010cf7c6:f8ec78ccab17e2e0f0f95d67ea2372c3"

DEEPSEEK_BASE_URL = "https://api.deepseek.com/v1/chat/completions"

# 设置fal.ai API密钥
os.environ['FAL_KEY'] = FAL_API_KEY

def test_deepseek_api():
    """测试DeepSeek API"""
    print("🧪 测试DeepSeek API...")
    
    headers = {
        "Authorization": f"Bearer {DEEPSEEK_API_KEY}",
        "Content-Type": "application/json"
    }
    
    data = {
        "model": "deepseek-chat",
        "messages": [
            {
                "role": "system", 
                "content": "你是一个专业的AI图像生成提示词专家。请将用户的描述转换为优化的英文提示词。"
            },
            {
                "role": "user", 
                "content": "一只可爱的小猫在花园里玩耍"
            }
        ],
        "temperature": 0.7,
        "max_tokens": 200
    }
    
    try:
        response = requests.post(DEEPSEEK_BASE_URL, headers=headers, json=data, timeout=30)
        response.raise_for_status()
        result = response.json()
        
        if 'choices' in result and len(result['choices']) > 0:
            prompt = result['choices'][0]['message']['content'].strip()
            print("✅ DeepSeek API测试成功!")
            print(f"📝 生成的提示词: {prompt}")
            return True
        else:
            print("❌ DeepSeek API返回格式异常")
            print(f"响应内容: {result}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ DeepSeek API请求失败: {e}")
        return False
    except Exception as e:
        print(f"❌ DeepSeek API测试出错: {e}")
        return False

def test_fal_text_to_image():
    """测试fal.ai文生图API"""
    print("\n🧪 测试fal.ai文生图API...")

    try:
        print("⏳ 正在生成图片，请稍候...")
        handler = fal_client.submit(
            "fal-ai/flux/dev",
            arguments={
                "prompt": "A cute cat playing in a beautiful garden, digital art, high quality",
                "image_size": "square_hd",
                "num_inference_steps": 20,  # 减少步数以加快测试
                "guidance_scale": 3.5,
                "num_images": 1,
                "enable_safety_checker": True
            }
        )
        result = handler.get()

        if result and 'images' in result and len(result['images']) > 0:
            print("✅ fal.ai文生图API测试成功!")
            print(f"📸 生成的图片URL: {result['images'][0]['url']}")
            print(f"📊 完整响应: {json.dumps(result, indent=2)}")
            return True
        else:
            print("❌ fal.ai API返回格式异常")
            print(f"响应内容: {result}")
            return False

    except Exception as e:
        print(f"❌ fal.ai API测试出错: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始API连接测试...\n")
    
    # 测试DeepSeek API
    deepseek_ok = test_deepseek_api()
    
    # 测试fal.ai API
    fal_ok = test_fal_text_to_image()
    
    # 总结测试结果
    print("\n" + "="*50)
    print("📋 测试结果总结:")
    print(f"DeepSeek API: {'✅ 正常' if deepseek_ok else '❌ 异常'}")
    print(f"fal.ai API: {'✅ 正常' if fal_ok else '❌ 异常'}")

    if deepseek_ok and fal_ok:
        print("\n🎉 所有API测试通过！应用可以正常使用。")
    else:
        print("\n⚠️  部分API测试失败，请检查网络连接和API密钥。")

if __name__ == "__main__":
    main()
