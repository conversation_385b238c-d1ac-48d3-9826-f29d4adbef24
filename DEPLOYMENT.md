# 部署指南

## 本地开发环境

### 1. 克隆项目
```bash
git clone <your-repo-url>
cd Flux对话式应用1.0
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 配置API密钥
编辑 `app.py` 文件中的API密钥：
```python
DEEPSEEK_API_KEY = "your-deepseek-api-key"
FAL_API_KEY = "your-fal-api-key"
```

### 4. 运行应用
```bash
python3 app.py
```

访问：http://127.0.0.1:5000

## 云端部署选项

### 选项1：Heroku部署

1. 创建 `Procfile`：
```
web: python app.py
```

2. 设置环境变量：
```bash
heroku config:set DEEPSEEK_API_KEY=your-key
heroku config:set FAL_API_KEY=your-key
```

3. 部署：
```bash
git add .
git commit -m "Deploy to Heroku"
git push heroku main
```

### 选项2：Railway部署

1. 连接GitHub仓库到Railway
2. 设置环境变量：
   - `DEEPSEEK_API_KEY`
   - `FAL_API_KEY`
3. Railway会自动部署

### 选项3：Render部署

1. 连接GitHub仓库到Render
2. 设置构建命令：`pip install -r requirements.txt`
3. 设置启动命令：`python app.py`
4. 设置环境变量

### 选项4：Vercel部署

1. 安装Vercel CLI：
```bash
npm i -g vercel
```

2. 部署：
```bash
vercel
```

## 环境变量配置

为了安全起见，建议使用环境变量存储API密钥：

### 修改app.py
```python
import os

DEEPSEEK_API_KEY = os.environ.get('DEEPSEEK_API_KEY', 'your-default-key')
FAL_API_KEY = os.environ.get('FAL_API_KEY', 'your-default-key')
```

### 本地开发
创建 `.env` 文件：
```
DEEPSEEK_API_KEY=your-deepseek-key
FAL_API_KEY=your-fal-key
```

然后安装python-dotenv：
```bash
pip install python-dotenv
```

在app.py中加载：
```python
from dotenv import load_dotenv
load_dotenv()
```

## 生产环境优化

### 1. 使用WSGI服务器
```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

### 2. 添加错误处理
```python
@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': '服务器内部错误'}), 500
```

### 3. 添加日志记录
```python
import logging
logging.basicConfig(level=logging.INFO)
```

### 4. 设置CORS（如果需要）
```bash
pip install flask-cors
```

```python
from flask_cors import CORS
CORS(app)
```

## 监控和维护

### 健康检查端点
```python
@app.route('/health')
def health_check():
    return jsonify({'status': 'healthy'}), 200
```

### 日志监控
- 使用云平台的日志服务
- 监控API调用频率和错误率
- 设置告警机制

## 安全注意事项

1. **API密钥安全**：
   - 不要在代码中硬编码API密钥
   - 使用环境变量或密钥管理服务
   - 定期轮换API密钥

2. **输入验证**：
   - 验证用户输入
   - 限制文件上传大小
   - 防止恶意内容

3. **速率限制**：
   - 实现API调用频率限制
   - 防止滥用

4. **HTTPS**：
   - 生产环境必须使用HTTPS
   - 保护数据传输安全

## 故障排除

### 常见问题

1. **端口占用**：
   - 更改端口：`app.run(port=8000)`

2. **依赖冲突**：
   - 使用虚拟环境
   - 检查requirements.txt版本

3. **API调用失败**：
   - 检查网络连接
   - 验证API密钥
   - 查看错误日志

4. **内存不足**：
   - 优化图片处理
   - 增加服务器内存
   - 实现缓存机制
