document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('generate-form');
    const textInput = document.getElementById('text-input');
    const imageInput = document.getElementById('image-input');
    const uploadBtn = document.getElementById('upload-btn');
    const imagePreview = document.getElementById('image-preview');
    const previewImg = document.getElementById('preview-img');
    const removeImageBtn = document.getElementById('remove-image');
    const generateBtn = document.getElementById('generate-btn');
    const chatMessages = document.getElementById('chat-messages');
    const loading = document.getElementById('loading');

    // 上传按钮点击事件
    uploadBtn.addEventListener('click', function() {
        imageInput.click();
    });

    // 图片选择事件
    imageInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImg.src = e.target.result;
                imagePreview.style.display = 'block';
                uploadBtn.textContent = '📎 更换图片';
            };
            reader.readAsDataURL(file);
        }
    });

    // 移除图片事件
    removeImageBtn.addEventListener('click', function() {
        imageInput.value = '';
        imagePreview.style.display = 'none';
        uploadBtn.textContent = '📎 上传图片';
    });

    // 表单提交事件
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const text = textInput.value.trim();
        const hasImage = imageInput.files.length > 0;
        
        // 验证输入
        if (!text && !hasImage) {
            showError('请提供文字描述或上传图片');
            return;
        }
        
        if (hasImage && !text) {
            showError('请为图片添加文字描述');
            return;
        }

        // 添加用户消息到聊天界面
        addUserMessage(text, hasImage);
        
        // 显示加载状态
        showLoading(true);
        generateBtn.disabled = true;

        // 准备表单数据
        const formData = new FormData();
        formData.append('text', text);
        if (hasImage) {
            formData.append('image', imageInput.files[0]);
        }

        // 发送请求
        fetch('/generate', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            showLoading(false);
            generateBtn.disabled = false;
            
            if (data.success) {
                addAssistantMessage(data);
                // 清空输入
                textInput.value = '';
                imageInput.value = '';
                imagePreview.style.display = 'none';
                uploadBtn.textContent = '📎 上传图片';
            } else {
                showError(data.error || '生成失败，请重试');
            }
        })
        .catch(error => {
            showLoading(false);
            generateBtn.disabled = false;
            console.error('Error:', error);
            showError('网络错误，请检查连接后重试');
        });
    });

    function addUserMessage(text, hasImage) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message user-message';
        
        let content = `<div class="message-content">`;
        if (hasImage) {
            content += `<p>🖼️ 图片 + 文字描述</p>`;
        }
        content += `<p>${escapeHtml(text)}</p></div>`;
        
        messageDiv.innerHTML = content;
        chatMessages.appendChild(messageDiv);
        scrollToBottom();
    }

    function addAssistantMessage(data) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message assistant-message';
        
        let content = `<div class="message-content">`;
        content += `<p><strong>生成类型：</strong>${data.generation_type}</p>`;
        
        if (data.optimized_prompt) {
            content += `<div class="prompt-info">`;
            content += `<strong>优化后的提示词：</strong><br>${escapeHtml(data.optimized_prompt)}`;
            content += `</div>`;
        }
        
        if (data.image_url) {
            content += `<div class="generated-image">`;
            content += `<img src="${data.image_url}" alt="生成的图片" loading="lazy">`;
            content += `</div>`;
        }
        
        content += `</div>`;
        messageDiv.innerHTML = content;
        chatMessages.appendChild(messageDiv);
        scrollToBottom();
    }

    function showError(message) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message assistant-message';
        messageDiv.innerHTML = `
            <div class="message-content">
                <div class="error-message">
                    ❌ ${escapeHtml(message)}
                </div>
            </div>
        `;
        chatMessages.appendChild(messageDiv);
        scrollToBottom();
    }

    function showLoading(show) {
        loading.style.display = show ? 'flex' : 'none';
    }

    function scrollToBottom() {
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 回车键提交（Ctrl+Enter 或 Cmd+Enter）
    textInput.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
            e.preventDefault();
            form.dispatchEvent(new Event('submit'));
        }
    });
});
