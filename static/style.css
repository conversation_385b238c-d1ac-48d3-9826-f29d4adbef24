* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

.chat-container {
    flex: 1;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    max-height: 400px;
    min-height: 200px;
}

.message {
    margin-bottom: 20px;
    animation: fadeIn 0.5s ease-in;
}

.system-message .message-content {
    background: #f8f9fa;
    border-left: 4px solid #667eea;
    padding: 15px;
    border-radius: 8px;
}

.user-message .message-content {
    background: #667eea;
    color: white;
    padding: 15px;
    border-radius: 15px 15px 5px 15px;
    margin-left: 20%;
}

.assistant-message .message-content {
    background: #f1f3f4;
    padding: 15px;
    border-radius: 15px 15px 15px 5px;
    margin-right: 20%;
}

.message-content ul {
    margin: 10px 0;
    padding-left: 20px;
}

.message-content li {
    margin: 5px 0;
}

.generated-image {
    margin: 15px 0;
    text-align: center;
}

.generated-image img {
    max-width: 100%;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.prompt-info {
    background: #e8f4f8;
    padding: 10px;
    border-radius: 8px;
    margin: 10px 0;
    font-size: 0.9rem;
}

.prompt-info strong {
    color: #2c5aa0;
}

.input-container {
    padding: 20px;
    border-top: 1px solid #eee;
    background: #fafafa;
}

.input-group {
    margin-bottom: 15px;
}

.text-input-container {
    margin-bottom: 15px;
}

#text-input {
    width: 100%;
    padding: 15px;
    border: 2px solid #ddd;
    border-radius: 10px;
    font-size: 16px;
    resize: vertical;
    min-height: 80px;
    font-family: inherit;
    transition: border-color 0.3s ease;
}

#text-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.file-input-container {
    display: flex;
    align-items: center;
    gap: 15px;
}

.upload-btn {
    background: #6c757d;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s ease;
}

.upload-btn:hover {
    background: #5a6268;
}

.image-preview {
    position: relative;
    display: inline-block;
}

.image-preview img {
    max-width: 100px;
    max-height: 100px;
    border-radius: 8px;
    border: 2px solid #ddd;
}

.remove-btn {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.generate-btn {
    width: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 15px;
    border-radius: 10px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.generate-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.generate-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.7);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    z-index: 1000;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255,255,255,0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

.error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #dc3545;
    margin: 10px 0;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .user-message .message-content,
    .assistant-message .message-content {
        margin-left: 0;
        margin-right: 0;
    }
    
    .file-input-container {
        flex-direction: column;
        align-items: stretch;
    }
}
